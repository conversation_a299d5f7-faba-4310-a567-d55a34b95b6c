"""
Transformer-based Trading Model for Advanced Market Analysis
State-of-the-art architecture for financial time series prediction
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import logging
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class TransformerConfig:
    """Configuration for transformer model"""
    d_model: int = 256
    num_heads: int = 8
    num_layers: int = 6
    d_ff: int = 1024
    dropout: float = 0.1
    max_seq_length: int = 512
    sequence_length: int = 512  # Add sequence_length alias for compatibility
    vocab_size: int = 1000  # For tokenized market features
    
class PositionalEncoding(nn.Module):
    """Positional encoding for transformer"""
    
    def __init__(self, d_model: int, max_len: int = 5000):
        super().__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return x + self.pe[:x.size(0), :]

class MultiHeadAttention(nn.Module):
    """Multi-head attention with financial market adaptations"""
    
    def __init__(self, d_model: int, num_heads: int, dropout: float = 0.1):
        super().__init__()
        assert d_model % num_heads == 0
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads
        
        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, query: torch.Tensor, key: torch.Tensor, value: torch.Tensor,
                mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        batch_size = query.size(0)
        
        # Linear transformations and reshape
        Q = self.w_q(query).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        K = self.w_k(key).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        V = self.w_v(value).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        
        # Attention
        attention_output, attention_weights = self._scaled_dot_product_attention(Q, K, V, mask)
        
        # Concatenate heads
        attention_output = attention_output.transpose(1, 2).contiguous().view(
            batch_size, -1, self.d_model
        )
        
        # Final linear transformation
        output = self.w_o(attention_output)
        
        return output, attention_weights
    
    def _scaled_dot_product_attention(self, Q: torch.Tensor, K: torch.Tensor, V: torch.Tensor,
                                    mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        d_k = Q.size(-1)
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(d_k)
        
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        output = torch.matmul(attention_weights, V)
        
        return output, attention_weights

class FeedForward(nn.Module):
    """Position-wise feed-forward network"""
    
    def __init__(self, d_model: int, d_ff: int, dropout: float = 0.1):
        super().__init__()
        self.linear1 = nn.Linear(d_model, d_ff)
        self.linear2 = nn.Linear(d_ff, d_model)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.linear2(self.dropout(F.relu(self.linear1(x))))

class TransformerBlock(nn.Module):
    """Single transformer block with market-specific adaptations"""
    
    def __init__(self, d_model: int, num_heads: int, d_ff: int, dropout: float = 0.1):
        super().__init__()
        
        self.attention = MultiHeadAttention(d_model, num_heads, dropout)
        self.feed_forward = FeedForward(d_model, d_ff, dropout)
        
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        # Self-attention with residual connection
        attn_output, attention_weights = self.attention(x, x, x, mask)
        x = self.norm1(x + self.dropout(attn_output))
        
        # Feed-forward with residual connection
        ff_output = self.feed_forward(x)
        x = self.norm2(x + self.dropout(ff_output))
        
        return x, attention_weights

class TransformerTradingModel(nn.Module):
    """
    Transformer-based model for financial trading
    Features:
    - Multi-head self-attention for temporal dependencies
    - Position-wise feed-forward networks
    - Residual connections and layer normalization
    - Market-specific adaptations
    - Multi-task learning capabilities
    """
    
    def __init__(self, config: TransformerConfig, input_size: int, output_size: int = 1):
        super().__init__()
        
        self.config = config
        self.input_size = input_size
        self.output_size = output_size
        
        # Input embedding and projection
        self.input_projection = nn.Linear(input_size, config.d_model)
        self.positional_encoding = PositionalEncoding(config.d_model, config.max_seq_length)
        
        # Transformer blocks
        self.transformer_blocks = nn.ModuleList([
            TransformerBlock(config.d_model, config.num_heads, config.d_ff, config.dropout)
            for _ in range(config.num_layers)
        ])
        
        # Output heads for multi-task learning
        self.price_head = nn.Sequential(
            nn.Linear(config.d_model, config.d_model // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.d_model // 2, 1)
        )
        
        self.volatility_head = nn.Sequential(
            nn.Linear(config.d_model, config.d_model // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.d_model // 2, 1)
        )
        
        self.direction_head = nn.Sequential(
            nn.Linear(config.d_model, config.d_model // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.d_model // 2, 3)  # Up, Down, Sideways
        )
        
        # Market regime classification head
        self.regime_head = nn.Sequential(
            nn.Linear(config.d_model, config.d_model // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.d_model // 2, 4)  # Bull, Bear, Sideways, High Vol
        )

        # Enhanced sentiment analysis heads
        self.sentiment_head = nn.Sequential(
            nn.Linear(config.d_model, config.d_model // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.d_model // 2, 1)  # Sentiment score prediction
        )

        self.fear_greed_head = nn.Sequential(
            nn.Linear(config.d_model, config.d_model // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.d_model // 2, 1)  # Fear & Greed Index prediction
        )

        self.market_stress_head = nn.Sequential(
            nn.Linear(config.d_model, config.d_model // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.d_model // 2, 1)  # Market stress level
        )

        # Composite sentiment fusion layer
        self.sentiment_fusion = nn.Sequential(
            nn.Linear(config.d_model + 3, config.d_model),  # +3 for sentiment outputs
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.d_model, config.d_model)
        )

        # Global pooling for sequence-level predictions
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        
        self._init_weights()
        
    def _init_weights(self):
        """Initialize weights"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
    
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None,
                return_attention: bool = False) -> Dict[str, torch.Tensor]:
        batch_size, seq_len, _ = x.shape
        _ = batch_size, seq_len  # Mark as used for shape validation
        
        # Input projection and positional encoding
        x = self.input_projection(x)
        x = self.positional_encoding(x.transpose(0, 1)).transpose(0, 1)
        
        # Transformer blocks
        attention_weights = []
        for transformer_block in self.transformer_blocks:
            x, attn_weights = transformer_block(x, mask)
            if return_attention:
                attention_weights.append(attn_weights)
        
        # Global representation (last token or pooled)
        global_repr = x[:, -1, :]  # Use last token
        pooled_repr = self.global_pool(x.transpose(1, 2)).squeeze(-1)  # Pooled representation
        
        # Enhanced sentiment predictions
        sentiment_pred = self.sentiment_head(global_repr)
        fear_greed_pred = self.fear_greed_head(global_repr)
        market_stress_pred = self.market_stress_head(global_repr)

        # Sentiment fusion for enhanced representation
        sentiment_features = torch.cat([sentiment_pred, fear_greed_pred, market_stress_pred], dim=-1)
        fused_repr = self.sentiment_fusion(torch.cat([global_repr, sentiment_features], dim=-1))

        # Multi-task outputs with enhanced sentiment integration
        outputs = {
            'price_prediction': self.price_head(fused_repr),  # Use fused representation
            'volatility_prediction': self.volatility_head(fused_repr),
            'direction_prediction': self.direction_head(fused_repr),
            'regime_prediction': self.regime_head(pooled_repr),
            'sentiment_prediction': sentiment_pred,
            'fear_greed_prediction': fear_greed_pred,
            'market_stress_prediction': market_stress_pred,
            'embeddings': x,
            'global_representation': global_repr,
            'fused_representation': fused_repr
        }
        
        if return_attention:
            outputs['attention_weights'] = attention_weights
        
        return outputs
    
    def predict_multi_horizon(self, x: torch.Tensor, horizons: List[int]) -> Dict[str, torch.Tensor]:
        """Predict multiple time horizons"""
        base_outputs = self.forward(x)
        
        multi_horizon_predictions = {}
        
        for horizon in horizons:
            # Create horizon-specific head if not exists
            horizon_head_name = f'horizon_{horizon}_head'
            if not hasattr(self, horizon_head_name):
                setattr(self, horizon_head_name, nn.Sequential(
                    nn.Linear(self.config.d_model, self.config.d_model // 2),
                    nn.ReLU(),
                    nn.Dropout(self.config.dropout),
                    nn.Linear(self.config.d_model // 2, 1)
                ))
            
            horizon_head = getattr(self, horizon_head_name)
            multi_horizon_predictions[f'horizon_{horizon}'] = horizon_head(base_outputs['global_representation'])
        
        return multi_horizon_predictions
    
    def get_attention_patterns(self, x: torch.Tensor) -> Dict[str, Any]:
        """Analyze attention patterns for interpretability"""
        outputs = self.forward(x, return_attention=True)
        attention_weights = outputs['attention_weights']
        
        # Aggregate attention across layers and heads
        layer_attention = []
        for layer_attn in attention_weights:
            # Average across heads
            avg_attn = layer_attn.mean(dim=1)  # [batch, seq_len, seq_len]
            layer_attention.append(avg_attn.cpu().numpy())
        
        return {
            'layer_attention': layer_attention,
            'attention_entropy': [self._calculate_attention_entropy(attn) for attn in layer_attention],
            'attention_focus': [self._calculate_attention_focus(attn) for attn in layer_attention]
        }
    
    def _calculate_attention_entropy(self, attention_matrix: np.ndarray) -> float:
        """Calculate attention entropy for diversity measurement"""
        # Average across batch and sequence dimensions
        avg_attention = attention_matrix.mean(axis=(0, 1))
        entropy = -np.sum(avg_attention * np.log(avg_attention + 1e-8))
        return float(entropy)
    
    def _calculate_attention_focus(self, attention_matrix: np.ndarray) -> float:
        """Calculate attention focus (inverse of spread)"""
        # Calculate how focused the attention is
        max_attention = attention_matrix.max(axis=-1).mean()
        return float(max_attention)
    
    def save_model(self, path: str):
        """Save model with configuration"""
        torch.save({
            'model_state_dict': self.state_dict(),
            'config': self.config,
            'input_size': self.input_size,
            'output_size': self.output_size
        }, path)
        logger.info(f"Transformer model saved to {path}")
    
    def load_model(self, path: str):
        """Load model with configuration"""
        checkpoint = torch.load(path, map_location='cpu')
        self.load_state_dict(checkpoint['model_state_dict'])
        logger.info(f"Transformer model loaded from {path}")
    
    def get_model_complexity(self) -> Dict[str, int]:
        """Get model complexity metrics"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'num_layers': self.config.num_layers,
            'num_heads': self.config.num_heads,
            'd_model': self.config.d_model
        }

    def adaptive_forward(self, x: torch.Tensor, market_regime: str = 'normal',
                        volatility_level: float = 0.5) -> Dict[str, torch.Tensor]:
        """Adaptive forward pass based on market conditions"""
        # Adjust model behavior based on market regime
        if market_regime == 'high_volatility':
            # Use higher dropout for regularization
            original_dropout = self.config.dropout
            self.config.dropout = min(0.5, original_dropout * 1.5)
        elif market_regime == 'low_volatility':
            # Use lower dropout for more sensitivity
            original_dropout = self.config.dropout
            self.config.dropout = max(0.05, original_dropout * 0.5)

        # Standard forward pass
        outputs = self.forward(x)

        # Restore original dropout
        if market_regime in ['high_volatility', 'low_volatility']:
            self.config.dropout = original_dropout

        # Adjust predictions based on volatility
        volatility_factor = 1.0 + (volatility_level - 0.5) * 0.2
        outputs['price_prediction'] = outputs['price_prediction'] * volatility_factor
        outputs['volatility_prediction'] = outputs['volatility_prediction'] * volatility_factor

        return outputs

    def ensemble_predict(self, x: torch.Tensor, num_samples: int = 5) -> Dict[str, torch.Tensor]:
        """Ensemble prediction using Monte Carlo dropout"""
        self.train()  # Enable dropout for uncertainty estimation

        predictions = []
        for _ in range(num_samples):
            with torch.no_grad():
                pred = self.forward(x)
                predictions.append(pred)

        self.eval()  # Return to eval mode

        # Aggregate predictions
        ensemble_outputs = {}
        for key in predictions[0].keys():
            if key in ['embeddings', 'attention_weights']:
                continue  # Skip non-aggregatable outputs

            stacked = torch.stack([p[key] for p in predictions])
            ensemble_outputs[f'{key}_mean'] = stacked.mean(dim=0)
            ensemble_outputs[f'{key}_std'] = stacked.std(dim=0)
            ensemble_outputs[f'{key}_confidence'] = 1.0 / (1.0 + stacked.std(dim=0))

        return ensemble_outputs

    def get_feature_importance(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """Calculate feature importance using integrated gradients"""
        x.requires_grad_(True)

        # Forward pass
        outputs = self.forward(x)
        price_pred = outputs['price_prediction']

        # Calculate gradients
        gradients = torch.autograd.grad(
            outputs=price_pred.sum(),
            inputs=x,
            create_graph=True,
            retain_graph=True
        )[0]

        # Feature importance as absolute gradient values
        feature_importance = torch.abs(gradients).mean(dim=0)

        return {
            'feature_importance': feature_importance,
            'gradient_norm': torch.norm(gradients, dim=-1),
            'input_sensitivity': torch.abs(gradients * x).mean(dim=0)
        }

    def optimize_for_inference(self):
        """Optimize model for faster inference"""
        # Convert to eval mode
        self.eval()

        # Fuse batch normalization if present
        # (Not applicable for this model but good practice)

        # Enable JIT compilation for faster inference
        try:
            dummy_input = torch.randn(1, 100, self.input_size)
            self.traced_model = torch.jit.trace(self, dummy_input)
            logger.info("✅ [TRANSFORMER] Model optimized with JIT compilation")
        except Exception as e:
            logger.warning(f"⚠️ [TRANSFORMER] JIT optimization failed: {e}")
            self.traced_model = None

    def fast_inference(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """Fast inference using optimized model"""
        if hasattr(self, 'traced_model') and self.traced_model is not None:
            try:
                # Use traced model for faster inference
                with torch.no_grad():
                    return self.traced_model(x)
            except Exception as e:
                logger.warning(f"⚠️ [TRANSFORMER] Traced model failed, using standard: {e}")

        # Fallback to standard inference
        with torch.no_grad():
            return self.forward(x)

    def get_memory_usage(self) -> Dict[str, float]:
        """Get model memory usage statistics"""
        if torch.cuda.is_available():
            torch.cuda.synchronize()
            memory_allocated = torch.cuda.memory_allocated() / 1024**2  # MB
            memory_cached = torch.cuda.memory_reserved() / 1024**2  # MB

            return {
                'gpu_memory_allocated_mb': memory_allocated,
                'gpu_memory_cached_mb': memory_cached,
                'model_size_mb': sum(p.numel() * p.element_size() for p in self.parameters()) / 1024**2
            }
        else:
            return {
                'model_size_mb': sum(p.numel() * p.element_size() for p in self.parameters()) / 1024**2
            }

    def validate_predictions(self, predictions: Dict[str, torch.Tensor]) -> Dict[str, bool]:
        """Validate prediction outputs for sanity checks"""
        validation_results = {}

        # Check for NaN values
        for key, tensor in predictions.items():
            if isinstance(tensor, torch.Tensor):
                validation_results[f'{key}_no_nan'] = not torch.isnan(tensor).any()
                validation_results[f'{key}_finite'] = torch.isfinite(tensor).all()

        # Check prediction ranges
        if 'price_prediction' in predictions:
            price_pred = predictions['price_prediction']
            validation_results['price_reasonable'] = (price_pred > -1.0).all() and (price_pred < 1.0).all()

        if 'volatility_prediction' in predictions:
            vol_pred = predictions['volatility_prediction']
            validation_results['volatility_positive'] = (vol_pred >= 0).all()

        return validation_results

    def predict_next_values(self, sequence, num_predictions: int = 1) -> List[float]:
        """Predict next values in a sequence using the transformer model"""
        try:
            import torch
            import numpy as np

            # Handle different input types
            if isinstance(sequence, dict):
                # Extract sequence from dict
                if 'features' in sequence:
                    sequence_data = sequence['features']
                elif 'sequence' in sequence:
                    sequence_data = sequence['sequence']
                else:
                    # Use first numeric value found
                    sequence_data = []
                    for key, value in sequence.items():
                        if isinstance(value, (list, tuple)):
                            sequence_data = list(value)
                            break
                        elif isinstance(value, (int, float)):
                            sequence_data = [value]
                            break
                    if not sequence_data:
                        sequence_data = [0.0]
            elif isinstance(sequence, (list, tuple)):
                sequence_data = list(sequence)
            else:
                # Single value
                sequence_data = [float(sequence)]

            # Convert sequence to tensor
            if len(sequence_data) < self.config.sequence_length:
                # Pad sequence if too short
                padded_sequence = [0.0] * (self.config.sequence_length - len(sequence_data)) + sequence_data
            else:
                # Take last sequence_length values
                padded_sequence = sequence_data[-self.config.sequence_length:]

            # Normalize sequence
            seq_array = np.array(padded_sequence)
            if seq_array.std() > 0:
                seq_normalized = (seq_array - seq_array.mean()) / seq_array.std()
            else:
                seq_normalized = seq_array

            # Convert to tensor and add batch dimension
            x = torch.FloatTensor(seq_normalized).unsqueeze(0).unsqueeze(-1)  # [1, seq_len, 1]

            # Expand to input_size if needed
            if x.size(-1) < self.input_size:
                x = x.repeat(1, 1, self.input_size)

            predictions = []
            current_input = x

            # Generate predictions iteratively
            for _ in range(num_predictions):
                with torch.no_grad():
                    output = self.forward(current_input)

                    # Get the main prediction
                    if 'price_prediction' in output:
                        next_val = output['price_prediction'][0, -1, 0].item()
                    elif 'prediction' in output:
                        next_val = output['prediction'][0, -1, 0].item()
                    else:
                        # Fallback to first output
                        next_val = list(output.values())[0][0, -1, 0].item()

                    predictions.append(next_val)

                    # Update input for next prediction
                    if num_predictions > 1:
                        # Shift sequence and add new prediction
                        new_input = torch.cat([
                            current_input[:, 1:, :],  # Remove first element
                            torch.FloatTensor([[[next_val] * self.input_size]])  # Add prediction
                        ], dim=1)
                        current_input = new_input

            return predictions

        except Exception as e:
            logger.error(f"[TRANSFORMER] Error in predict_next_values: {e}")
            # Return simple linear extrapolation as fallback
            if len(sequence) >= 2:
                trend = sequence[-1] - sequence[-2]
                return [sequence[-1] + trend * (i + 1) for i in range(num_predictions)]
            else:
                return [sequence[-1] if sequence else 0.0] * num_predictions

    def update_with_market_data(self, market_data: dict):
        """Update model with new market data for continuous learning"""
        try:
            # Extract features from market data
            features = []

            # Basic price features
            if 'price' in market_data:
                features.append(float(market_data['price']))
            if 'volume' in market_data:
                features.append(float(market_data.get('volume', 0)))
            if 'volatility' in market_data:
                features.append(float(market_data.get('volatility', 0)))

            # Ensure we have enough features
            while len(features) < self.input_size:
                features.append(0.0)

            # Truncate if too many features
            features = features[:self.input_size]

            # Convert to tensor and update internal state
            feature_tensor = torch.FloatTensor([features]).unsqueeze(0)

            # Store for potential training (simplified update)
            if not hasattr(self, '_recent_data'):
                self._recent_data = []

            self._recent_data.append(feature_tensor)

            # Keep only recent data (last 100 samples)
            if len(self._recent_data) > 100:
                self._recent_data = self._recent_data[-100:]

            logger.debug(f"[TRANSFORMER] Updated with market data: {len(features)} features")

        except Exception as e:
            logger.error(f"[TRANSFORMER] Error updating with market data: {e}")
            # Silently continue - this is not critical
